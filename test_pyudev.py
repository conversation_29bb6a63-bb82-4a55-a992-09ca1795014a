#!/usr/bin/env python3
"""
测试pyudev USB设备监控功能
"""
import pyudev
import time

def test_usb_monitoring():
    """测试USB设备监控"""
    print("开始测试USB设备监控...")
    print("请插拔USB设备进行测试...")
    
    try:
        context = pyudev.Context()
        monitor = pyudev.Monitor.from_netlink(context)
        monitor.filter_by(subsystem='usb')
        
        print("USB监控已启动，等待设备事件...")
        
        for device in iter(monitor.poll, None):
            if device.action in ['add', 'remove']:
                vendor_id = device.get('ID_VENDOR_ID', '')
                serial = device.get('ID_SERIAL_SHORT', '')
                usb位置 = device.get('DEVPATH', '').split('/')[-1]
                
                print(f"事件: {device.action}")
                print(f"厂商ID: {vendor_id}")
                print(f"序列号: {serial}")
                print(f"USB位置: {usb位置}")
                print(f"设备路径: {device.get('DEVPATH', '')}")
                print("-" * 50)
                
                # 检查是否为fastboot设备
                if serial and serial.isdigit() and vendor_id in ['0482', '18d1']:
                    print(f"发现Fastboot设备: {serial} 位置: {usb位置}")
                    
    except KeyboardInterrupt:
        print("\n监控已停止")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == '__main__':
    test_usb_monitoring()
