{"models": {"901kc": {"display_name": "901KC", "commands": ["--set-active=a", "flash aboot aboot.bin", "flash boot boot.bin", "flash carrier carrier.bin", "flash cmnlib cmnlib.bin", "flash cmnlib64 cmnlib64.bin", "flash config config.bin", "flash devcfg devcfg.bin", "flash dsp dsp.bin", "flash dtbo dtbo.bin", "flash keymaster keymaster.bin", "flash mdtp mdtp.bin", "flash misc misc.bin", "flash modem modem.bin", "flash persist persist.bin", "flash reserve4 reserve4.bin", "flash rpm rpm.bin", "flash sbl1 sbl1.bin", "flash sysprop sysprop.bin", "flash tz tz.bin", "flash vbmeta vbmeta.bin", "flash product product.img", "flash vendor vendor.img", "flash system system.img"]}, "51b": {"display_name": "51B", "commands": ["--set-active=a", "flash boot_a boot.img", "flash dtbo dtbo.img", "flash modem_a modem.img", "flash vbmeta vbmeta.img", "flash vbmeta_system vbmeta_system.img", "flash vendor_boot vendor_boot.img", "erase super", "flash super super.img"]}, "101kc": {"display_name": "101KC", "commands": ["--set-active=a", "flash dtbo dtbo.img", "flash modem_a modem.img", "flash vbmeta vbmeta.img", "flash vbmeta_system vbmeta_system.img", "flash core_nhlos core_nhlos.img", "flash vendor_boot vendor_boot.img", "erase super", "flash super super.img"]}}}