# Fastboot刷写工具 - Linux版本

## 主要变更

### 移除的Windows特定功能
- ✅ 移除WMI和pythoncom依赖
- ✅ 移除USB优化和清理功能
- ✅ 移除管理员权限检查和DPI设置
- ✅ 移除Windows注册表操作

### 新增的Linux功能
- ✅ 使用pyudev进行USB设备监控
- ✅ 新增USB位置显示功能
- ✅ 改进窗格标题显示（USB位置 + SN）

### 窗格标题改进
- USB位置使用深蓝色（#0d47a1）显示，更加突出
- SN保持原有白色显示
- 两者在同一行显示，便于区分设备

## 安装和使用

### 1. 安装依赖
```bash
# 运行安装脚本
./install_linux.sh

# 或手动安装
pip3 install pyudev
```

### 2. 准备文件
确保以下文件存在：
- `fastboot` - Linux版本的fastboot可执行文件
- `config.json` - 设备配置文件
- `chkcode.bin` - 解锁文件（如需要）

### 3. 运行程序
```bash
python3 jcflash.py
```

### 4. 测试USB监控
```bash
python3 test_pyudev.py
```

## 技术细节

### USB设备监控
- 使用pyudev监控USB设备插拔
- 支持VID_0482和VID_18D1设备
- 自动获取USB物理位置信息

### 代码风格
- 保持原有极简优雅风格
- 中文命名习惯不变
- 功能直接有效，无过度设计

## 注意事项

1. 需要Python 3.6+
2. 需要安装pyudev库
3. fastboot需要可执行权限
4. 某些操作可能需要sudo权限

## 故障排除

### USB设备无法识别
1. 检查设备权限：`lsusb`
2. 检查udev规则
3. 运行测试脚本：`python3 test_pyudev.py`

### 权限问题
```bash
# 添加用户到plugdev组
sudo usermod -a -G plugdev $USER

# 重新登录或重启
```
