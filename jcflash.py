#!/usr/bin/env python3
import asyncio
import datetime
import logging
import os
import subprocess
import sys
import threading
import time
import tkinter as tk
from tkinter import Tk, Label, Frame, messagebox, StringVar, OptionMenu, Menu
from tkinter.ttk import Progressbar
from tkinter import Scrollbar, Canvas
import pyudev
import json

# 配置日志系统 - 只输出到控制台
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(message)s',
    datefmt='%H:%M:%S'
)
logger = logging.getLogger(__name__)


def 时间():
    return f'{datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")} - '


class 格子:
    def __init__(self, 窗口):
        self.窗口 = 窗口
        self.设备ID = ''
        self.进度条 = None
        self.状态标签 = None
        self.开始时间 = 0

    def 创建(self, 所在位置):
        self.窗格所在位置 = 所在位置
        self.状态 = "等待设备连接..."

        # 创建窗格，无外边距，紧密排列
        self.窗格 = Frame(self.窗口, width=格子窗口宽度, height=格子窗口高度,
                          borderwidth=1, relief="raised", bg='#f8f9fa')
        self.窗格.pack_propagate(0)
        self.窗格.pack(side='left')

        # 创建标题容器框架
        self.标题容器 = Frame(self.窗格, bg='#1976d2')
        self.标题容器.pack(fill='x', padx=1, pady=(1, 0))

        # USB位置标签（左侧，更突出）
        self.usb位置标签 = Label(self.标题容器, text="",
                                fg='#0d47a1', font=('Arial', 窗口字体大小, 'bold'),
                                bg='#1976d2')
        self.usb位置标签.pack(side='left', padx=(5, 2))

        # 创建标题标签（SN显示）
        self.标题标签 = Label(self.标题容器, text=f"{self.窗格所在位置} - 空闲",
                              fg='white', font=('Arial', 窗口字体大小, 'bold'),
                              bg='#1976d2')
        self.标题标签.pack(side='left', fill='both', expand=True)

        # 创建重新按钮（初始隐藏）
        self.重新按钮 = tk.Button(self.标题容器, text="重新",
                                  font=('Arial', max(6, 窗口字体大小-2), 'bold'),
                                  bg='#ff5722', fg='white', relief='flat',
                                  padx=2, pady=0, command=self.重新刷写)
        # 初始不显示重新按钮

        # 创建进度条
        self.进度条 = Progressbar(self.窗格, orient='horizontal',
                                  length=格子窗口宽度-10, mode='determinate')
        self.进度条.pack(pady=1)

        # 创建状态标签
        self.状态标签 = Label(self.窗格, text=self.状态, fg='#666666',
                              font=('Arial', 窗口字体大小),
                              wraplength=格子窗口宽度-10, justify='center',
                              bg='#f8f9fa')
        self.状态标签.pack(expand=True, fill='both', padx=1, pady=(0, 1))

        # 创建右键菜单
        menu_font = ('Arial', 窗口字体大小, 'bold')
        self.右键菜单 = Menu(self.窗格, tearoff=0, font=menu_font)
        self.右键菜单.add_command(label=f'清除{self.窗格所在位置}窗格信息', command=self.清除设备信息)
        self.右键菜单.add_command(label=f'重启设备', command=self.重启)

        # 绑定右键点击事件
        self.窗格.bind("<Button-3>", self.显示右键菜单)
        self.usb位置标签.bind("<Button-3>", self.显示右键菜单)
        self.标题标签.bind("<Button-3>", self.显示右键菜单)
        self.标题容器.bind("<Button-3>", self.显示右键菜单)
        self.状态标签.bind("<Button-3>", self.显示右键菜单)
        self.进度条.bind("<Button-3>", self.显示右键菜单)

    def 重启(self):
        if self.设备ID:
            self._执行重启逻辑()

    def 重新刷写(self):
        """重新按钮点击事件：移除完成记录并重启设备"""
        if self.设备ID:
            self._执行重启逻辑()

    def _执行重启逻辑(self):
        """统一的重启逻辑，被重启和重新刷写功能复用"""
        global fastboot, 完成
        process = subprocess.Popen([fastboot, '-s', self.设备ID, 'reboot'])
        start_time = time.time()
        while True:
            if process.poll() is not None:
                break
            if time.time() - start_time > 2:
                logger.warning(f"{self.设备ID} 执行重启超时")
                process.kill()
                break
            time.sleep(0.1)
        完成.pop(self.设备ID, None)

    def 显示右键菜单(self, event):
        self.右键菜单.post(event.x_root, event.y_root)

    def 显示设备信息(self, 设备ID, usb位置=None):
        self.设备ID = 设备ID
        # 更新USB位置显示（仅在创建时设置一次）
        if usb位置:
            self.usb位置标签.config(text=f"{usb位置}")
        # 更新标题标签显示SN号（橙色表示执行中）
        self.标题标签.config(text=f"SN: {设备ID}", bg='#ff9800', fg='white')
        self.标题容器.config(bg='#ff9800')
        # 隐藏重新按钮（执行中状态）
        self.重新按钮.pack_forget()
        # 更新状态标签显示详细信息
        self.状态 = "发现设备-初始化中...\n稍等...\n\n若长时间无响应\n请重启设备"
        self.状态标签.config(text=self.状态, fg='#1976d2')
        self.进度条['value'] = 0
        self.进度条.update()
        self.开始时间 = time.time()

    def 获取当前时间(self):
        return time.strftime(f"%H:%M:%S", time.localtime())

    def 显示错误信息(self, 错误信息):
        # 更新标题标签显示错误状态
        self.标题标签.config(text=f"SN: {self.设备ID}", bg='#f44336', fg='white')
        self.标题容器.config(bg='#f44336')
        # 隐藏重新按钮（错误状态）
        self.重新按钮.pack_forget()
        # 更新状态标签显示错误详情
        self.状态 = "{}-刷写失败\n{}".format(错误信息, self.获取当前时间())
        self.状态标签.config(text=self.状态, fg='red')
        self.进度条.stop()

    def 显示成功信息(self, 设备ID, 耗时秒数):
        global 完成
        # 检查是否为重复刷写
        if 设备ID in 完成 and "时刷写过" in str(耗时秒数):
            # 重复刷写状态 - 使用紫色背景区分
            self.标题标签.config(text=f"SN: {设备ID}", bg='#9c27b0', fg='white')
            self.标题容器.config(bg='#9c27b0')
            # 显示重新按钮
            self.重新按钮.pack(side='right', padx=2)
            # 更新状态标签显示重复刷写信息
            self.状态 = f"{耗时秒数}"
            self.状态标签.config(text=self.状态, fg='#9c27b0')
        else:
            # 正常完成状态 - 绿色背景
            self.标题标签.config(text=f"SN: {设备ID}", bg='#4caf50', fg='white')
            self.标题容器.config(bg='#4caf50')
            # 隐藏重新按钮
            self.重新按钮.pack_forget()
            # 更新状态标签显示成功详情
            self.状态 = "{}-刷写成功\n请拔掉设备\n\n耗时：{}秒".format(self.获取当前时间(), 耗时秒数)
            self.状态标签.config(text=self.状态, fg='green')

    def 清除设备信息(self):
        logger.info(f"{self.窗格所在位置} 窗格信息已清除")
        self.设备ID = ''
        # 重置标题标签和容器
        self.标题标签.config(text=f"{self.窗格所在位置} - 空闲", bg='#1976d2', fg='white')
        self.标题容器.config(bg='#1976d2')
        # 隐藏重新按钮
        self.重新按钮.pack_forget()
        # 重置状态标签
        self.状态 = "等待设备连接..."
        self.状态标签.config(text=self.状态, fg='#666666')
        self.进度条['value'] = 0
        self.进度条.update()


class Fastboot刷写工具:
    def __init__(self):
        self.格子数量 = 初始格子数量
        self.每行格子数 = 每行格子数

        self.窗口 = Tk()
        self.窗口.title(os.path.basename(os.getcwd()))

        # 初始化属性
        self.格子列表 = []
        self.设备格子映射 = {}
        self.stop = set()
        self.执行中 = set()
        self.格子锁 = threading.Lock()



        # 设置窗口尺寸和位置
        self.设置窗口尺寸()

        # 现在创建格子（在窗口尺寸设置之后）
        self.创建格子()

        # 定义速率选项
        self.速率选项 = StringVar(self.窗口)
        self.速率选项.set("满速率-64M")

        # 创建多选框
        self.速率菜单 = OptionMenu(self.窗口, self.速率选项,
                                   "高速率-32M", '满速率-64M', '极限速率-128M',
                                   '超级速率-256M', '极速模式-512M', '最高速率-1G', '不限速',
                                   command=self.打印选择)
        self.速率菜单.pack(side='bottom', fill='x')

        self.设备监视线程 = threading.Thread(target=self.监视_设备)
        self.设备监视线程.daemon = True
        self.设备监视线程.start()

        self.数量 = 0
        self.速率 = fastboot_sizes.get(self.速率选项.get())
        self.标题 = os.path.basename(os.getcwd())
        self.窗口.title(self.标题)
        self.窗口.mainloop()



    def 设置窗口尺寸(self):
        """设置主窗口全屏显示（避开任务栏）"""
        # 全屏显示，从顶部开始，高度已经减去了任务栏
        self.窗口.geometry(f'{主窗口宽度}x{主窗口高度}+0+0')

        # 禁止调整大小
        self.窗口.resizable(False, False)

    def 打印选择(self, value):
        self.速率 = fastboot_sizes.get(value)
        logger.info(f"刷写速率更新: {self.速率}")

    def 更新标题数量(self):
        with self.格子锁:
            self.数量 += 1
            self.窗口.title(self.标题 + f' - 已完成 - {str(self.数量)} 台')

    def 创建格子(self):
        # 创建滚动区域
        self.创建滚动区域()

        # 创建完整的网格布局，与预览完全一致
        格子编号 = 0
        for i in range(行数):
            行窗格 = Frame(self.滚动内容框架)
            行窗格.pack(pady=0)

            for j in range(self.每行格子数):
                if 格子编号 < 初始格子数量:
                    # 创建实际的设备窗格
                    格子实例 = 格子(行窗格)
                    self.格子列表.append(格子实例)
                    格子实例.创建(格子编号 + 1)
                else:
                    # 创建空窗格，无外边距，紧密排列
                    空窗格 = Frame(行窗格, width=格子窗口宽度, height=格子窗口高度,
                                  borderwidth=1, relief="solid", bg='#fafafa')
                    空窗格.pack_propagate(0)
                    空窗格.pack(side='left')

                    # 添加虚线效果的标签
                    空标签 = tk.Label(空窗格, text="空闲", font=('Arial', 窗口字体大小),
                                     fg='#cccccc', bg='#fafafa')
                    空标签.pack(expand=True)

                格子编号 += 1

        # 更新滚动区域
        self.更新滚动区域()

    def 创建滚动区域(self):
        """创建带滚动条的区域"""
        # 计算是否需要滚动条
        需要的高度 = 行数 * (格子窗口高度 + 2)  # 每行高度 + 边框
        可用高度 = 主窗口高度 - 80  # 减去速率菜单和边距

        if 需要的高度 <= 可用高度:
            # 不需要滚动条，直接使用窗口
            self.滚动内容框架 = self.窗口
            return

        # 需要滚动条
        # 创建主容器框架
        self.主容器 = Frame(self.窗口)
        self.主容器.pack(fill="both", expand=True, padx=0, pady=0)

        # 创建Canvas和滚动条
        self.画布 = Canvas(self.主容器, highlightthickness=0, bd=0)
        self.垂直滚动条 = Scrollbar(self.主容器, orient="vertical", command=self.画布.yview, width=16)
        self.滚动内容框架 = Frame(self.画布)

        # 配置滚动
        self.画布.configure(yscrollcommand=self.垂直滚动条.set)

        # 布局 - 紧密排列，无缝隙
        self.垂直滚动条.pack(side="right", fill="y")
        self.画布.pack(side="left", fill="both", expand=True)

        # 将内容框架放入画布
        self.画布窗口 = self.画布.create_window((0, 0), window=self.滚动内容框架, anchor="nw")

        # 绑定事件
        self.滚动内容框架.bind("<Configure>", self.配置滚动区域)
        self.画布.bind("<Configure>", self.配置画布大小)
        self.画布.bind_all("<MouseWheel>", self.鼠标滚轮)

    def 配置滚动区域(self, event):
        """配置滚动区域大小"""
        self.画布.configure(scrollregion=self.画布.bbox("all"))

    def 配置画布大小(self, event):
        """配置画布大小"""
        if hasattr(self, '画布'):
            画布宽度 = event.width
            self.画布.itemconfig(self.画布窗口, width=画布宽度)

    def 鼠标滚轮(self, event):
        """鼠标滚轮滚动"""
        if hasattr(self, '画布'):
            self.画布.yview_scroll(int(-1*(event.delta/120)), "units")

    def 更新滚动区域(self):
        """更新滚动区域"""
        if hasattr(self, '画布'):
            self.窗口.update_idletasks()
            self.画布.configure(scrollregion=self.画布.bbox("all"))

    def 监视_设备(self):
        context = pyudev.Context()
        monitor = pyudev.Monitor.from_netlink(context)
        monitor.filter_by(subsystem='usb')

        for device in iter(monitor.poll, None):
            if device.action == 'add':
                try:
                    vendor_id = device.get('ID_VENDOR_ID', '')
                    serial = device.get('ID_SERIAL_SHORT', '')
                    usb位置 = device.get('DEVPATH', '').split('/')[-1]

                    if serial and serial.isdigit() and vendor_id in ['0482', '18d1']:
                        if serial in self.设备格子映射:
                            logger.debug(f"{serial} 设备已存在")
                            continue
                        b = threading.Thread(target=self.处理_fastboot设备1, args=(serial, usb位置), name=serial)
                        b.daemon = True
                        b.start()
                except:
                    continue
            elif device.action == 'remove':
                try:
                    serial = device.get('ID_SERIAL_SHORT', '')
                    if serial and serial.isdigit():
                        with self.格子锁:
                            格子实例 = self.设备格子映射.pop(serial, None)
                            if 格子实例:
                                asyncio.run(self.处理断开(serial, 格子实例))
                except:
                    continue

    async def 处理断开(self, DeviceID, 格子实例):
        self.stop.add(DeviceID)
        await asyncio.sleep(1)
        格子实例.清除设备信息()
        for thread in threading.enumerate():
            if thread.name == DeviceID:
                logger.info(f"{DeviceID} 设备断开")
                if 格子实例:
                    格子实例.清除设备信息()
                break

        if DeviceID in str(格子实例.状态):
            格子实例.清除设备信息()
        self.stop.remove(DeviceID)

    def runcmd(self, command, timeout=5):
        try:
            result = subprocess.run(command,
                                  shell=True,
                                  capture_output=True,
                                  text=True,
                                  encoding='utf-8',
                                  errors='replace',
                                  timeout=timeout)
            output = result.stdout.strip() + result.stderr.strip()
            returncode = result.returncode
        except subprocess.TimeoutExpired:
            output = f'{command}超时'
            returncode = -1
        return returncode, output

    def 处理_fastboot设备1(self, 设备ID, usb位置=None):
        global fastboot, 解bl, 选择型号, chkcode
        # 查找空闲的格子
        空闲格子 = self.得到窗格(设备ID, usb位置)
        if not 空闲格子:
            return
        logger.info(f"SN:{设备ID} 刷写开始")
        ud = self.runcmd(f'{fastboot} -s {设备ID} getvar unlocked', 8)
        if 'unlocked: no' in ud[1]:
            if 设备ID not in 解bl:
                logger.info(f"SN:{设备ID} 开始解BL")
                解BL命令 = [
                    f'{fastboot} -s {设备ID}  flash chkcode {chkcode}',
                    f'{fastboot} -s {设备ID}  reboot bootloader',
                ]
                a901kc解BL命令 = [
                    f'{fastboot} -s {设备ID}  flash config config.bin',
                    f'{fastboot} -s {设备ID}  flash reserve4 reserve4.bin',
                    f'{fastboot} -s {设备ID}  reboot bootloader',
                ]
                if '901kc' in 选择型号:
                    logger.info(f"SN:{设备ID} 901kc解BL")
                    解BL命令 = a901kc解BL命令
                for 解 in 解BL命令:
                    j = self.runcmd(解)
                    if j[0] != 0:
                        空闲格子.显示错误信息(f"{j[1]}\nBL解锁失败,重试!")
                        return
                    空闲格子.状态标签.config(
                        text="解BL中\n{}\n".format(j[1]))
                解bl.add(设备ID)
                return
        u = self.runcmd(f'{fastboot} -s {设备ID} oem device-info', timeout=2)
        if u[0] == -1:
            空闲格子.显示错误信息("重启或重新拔插!")
            return
        logger.debug(f"{设备ID} 设备信息: {u[1]}")
        if not self.刷写执行(空闲格子, 设备ID):
            return
        空闲格子.状态标签.config(text="清除数据中...")
        # 清除数据重启
        清理 = [
            f'{fastboot} -s {设备ID} erase frp',
            f'{fastboot} -s {设备ID} -w',
        ]
        for i in 清理:
            self.runcmd(i)
        j = self.runcmd(f'{fastboot} -s {设备ID} flashing unlock')
        if j[0] == 0 or 'unlocked' in j[1]:
            完成[设备ID] = 时间()
            耗时秒数 = self.计算耗时秒数(空闲格子.开始时间)
            空闲格子.显示成功信息(设备ID, 耗时秒数)
            self.更新标题数量()
            logger.info(f"SN:{设备ID} 刷写完毕")
        else:
            空闲格子.显示错误信息("重新进一下模式")
            完成[设备ID] = 时间()
            return

    def 得到窗格(self, 设备ID, usb位置=None):
        # 查找空闲的格子
        空闲格子 = None
        with self.格子锁:
            for 格子实例 in self.格子列表:
                if 格子实例.设备ID == 设备ID:
                    if 设备ID in self.执行中:
                        self.执行中.remove(设备ID)
                        return 格子实例
                    格子实例.清除设备信息()
            for 格子实例 in self.格子列表:
                if 格子实例.设备ID == '':
                    空闲格子 = 格子实例
                    break
            if 空闲格子 is None:
                messagebox.showinfo('请注意,窗格已经插满了!', '不要插多超过窗格的机器-请拔掉这台设备')
                return False
            # 存储设备和格子的对应关系
            空闲格子.显示设备信息(设备ID, usb位置)
            self.设备格子映射[设备ID] = 空闲格子
        return 空闲格子

    def 刷写执行(self, 空闲格子, 设备ID):
        global 完成, config, 选择型号, fastboot
        if o := 完成.get(设备ID, False):
            空闲格子.进度条['value'] = 100
            空闲格子.进度条.update()
            空闲格子.显示成功信息(设备ID, f'{o}时刷写过\n点击"重新"按钮可重刷')
            logger.info(f"SN:{设备ID} 之前已刷写过，跳过")
            return False
        百分比 = 0
        # 从配置文件获取命令列表
        命令数 = len(config['models'][选择型号]['commands'])
        e = False
        行 = ''
        空闲格子.进度条['value'] = 百分比 + 5
        空闲格子.进度条.update()

        for j, i in enumerate(config['models'][选择型号]['commands'], start=1):
            if e:
                break
            i = i.split()
            if self.速率 != '0':
                命令 = [fastboot, "-s", 设备ID, '-S', self.速率] + i
            else:
                命令 = [fastboot, "-s", 设备ID] + i

            进程 = subprocess.Popen(命令, stdout=subprocess.PIPE,
                                    stderr=subprocess.STDOUT, text=True)
            while True:
                if 设备ID in self.stop:
                    logger.warning(f"SN:{设备ID} 设备断开，终止任务")
                    空闲格子.显示错误信息("设备已断开")
                    # 进程.terminate()  # 尝试优雅地终止进程
                    # 进程.wait()  # 等待进程完全结束
                    进程.kill()
                    return
                try:
                    行 = 进程.stdout.readline()
                except Exception as e:
                    e = True
                    进程.terminate()  # 尝试优雅地终止进程
                    进程.wait()  # 等待进程完全结束
                    break
                if 进程.poll() is not None:  # 检查进程是否已结束
                    break
                if 行:
                    耗时秒数 = self.计算耗时秒数(空闲格子.开始时间)
                    if 'Sending' in 行:
                        try:
                            parts = 行.split('/')
                            if len(parts) >= 2:
                                current = int(parts[-2].split()[-1])
                                total = int(parts[-1].split()[0])
                                progress = (current / total) * 100
                                partition = 行.split("'")[1]
                                空闲格子.状态标签.config(
                                    text=f"正在写入{partition}\n{current}/{total} ({progress:.1f}%)\n命令{j}/{命令数} 剩余{命令数-j}个\n已用时{耗时秒数}s")
                                空闲格子.进度条['value'] = progress
                                continue
                        except Exception as e:
                            logger.error(f"SN:{设备ID} 进度信息处理失败: {str(e)}")
                            continue
                        空闲格子.状态标签.config(
                            text="刷写 {} 中\n{}\n命令{}/{} 剩余{}个\n已用时：{}秒".format(
                                i[-1], 行, j, 命令数, 命令数-j, 耗时秒数))
                    if logger.isEnabledFor(logging.DEBUG):
                        logger.debug(f"SN:{设备ID} {行.strip()}")
                    if 'error' in 行.lower() or 'failed' in 行.lower():
                        if 设备ID in self.stop:
                            logger.warning(f"SN:{设备ID} 设备断开，终止任务")
                            空闲格子.显示错误信息("设备已断开")
                            进程.kill()
                            return False
                        if 'battery' in 行.lower():
                            空闲格子.显示错误信息("电池电量不足!\n请充电后再刷!")
                            进程.kill()
                            return False
                        e = True
                        进程.terminate()  # 尝试优雅地终止进程
                        进程.wait()  # 等待进程完全结束
                        break
                time.sleep(0.1)  # 优化UI响应性
            百分比 = int((j / 命令数) * 100)
            空闲格子.进度条['value'] = 百分比
            空闲格子.进度条.update()
        try:
            if 进程.returncode != 0 or 百分比 != 100:
                if 'Error reading sparse file' in 行:
                    空闲格子.显示错误信息(f"{行}\nUSB不稳定")
                    return False
                else:
                    空闲格子.显示错误信息(f"{行}")
                    return False
        except UnboundLocalError as e:  # 若是之前刷过 就没有创建进程,所以会错误.
            return True
        return True

    def 计算耗时秒数(self, 开始时间):
        return round(time.time() - 开始时间, 1)


def 加载配置文件():
    global config
    try:
        with open('config.json', 'r', encoding='utf-8') as f:
            config = json.load(f)

        # 验证配置文件格式
        if 'models' not in config:
            raise ValueError("配置文件缺少 'models' 字段")

        for model_id, model_info in config['models'].items():
            if not isinstance(model_info, dict):
                raise ValueError(f"型号 {model_id} 的配置必须是字典格式")
            if 'commands' not in model_info:
                raise ValueError(f"型号 {model_id} 缺少 'commands' 字段")
            if not isinstance(model_info['commands'], list):
                raise ValueError(f"型号 {model_id} 的 'commands' 必须是列表")
            # 验证每个命令是否为字符串
            for cmd in model_info['commands']:
                if not isinstance(cmd, str):
                    raise ValueError(f"型号 {model_id} 的命令必须是字符串")

        return config

    except FileNotFoundError:
        messagebox.showerror('错误', '未找到配置文件 config.json!')
        sys.exit()
    except json.JSONDecodeError:
        messagebox.showerror('错误', '配置文件格式错误!')
        sys.exit()
    except ValueError as e:
        messagebox.showerror('错误', f'配置文件验证失败: {str(e)}')
        sys.exit()


def 检测屏幕信息():
    """检测屏幕分辨率和工作区域"""
    root = tk.Tk()
    root.withdraw()
    屏幕宽度 = root.winfo_screenwidth()
    屏幕高度 = root.winfo_screenheight()
    root.destroy()
    return 屏幕宽度, 屏幕高度

def 计算实际窗格尺寸(每行数量, 行数):
    """统一的窗格尺寸计算函数，确保预览和实际一致"""
    屏幕宽度, 屏幕高度 = 检测屏幕信息()
    主窗口宽度 = 屏幕宽度
    主窗口高度 = 屏幕高度 - 80

    窗口边距 = 20
    速率菜单高度 = 40

    # 紧密排列，无间距
    可用宽度 = 主窗口宽度 - 窗口边距 * 2
    可用高度 = 主窗口高度 - 窗口边距 * 2 - 速率菜单高度

    # 充分利用空间，精确计算窗格尺寸
    边框宽度 = 2  # borderwidth=1，左右各1px

    # 优化窗格尺寸计算，确保日志区域有足够空间
    窗格宽度 = max(80, (可用宽度 - 每行数量 * 边框宽度) // 每行数量)

    # 计算理想的窗格高度，确保日志区域清晰可见
    计算高度 = (可用高度 - 行数 * 边框宽度) // 行数

    # 根据行数调整最小高度，确保日志区域足够
    if 行数 <= 8:
        最小高度 = 120  # 8行以内，保持较大高度
    elif 行数 <= 16:
        最小高度 = 90   # 16行以内，中等高度
    else:
        最小高度 = 80   # 超过16行，适当压缩但保证可读性

    窗格高度 = max(最小高度, 计算高度)
    窗格间距 = 0  # 紧密排列，无间距

    return 窗格宽度, 窗格高度, 窗格间距

def 初始窗口():
    # 检测屏幕信息
    屏幕宽度, 屏幕高度 = 检测屏幕信息()

    # 状态变量
    selected_device = 0
    selected_rows = 0
    selected_model = ""
    current_layout = None

    def 计算最佳窗口尺寸():
        """智能计算最佳窗口尺寸，充分利用屏幕空间"""
        # 预留系统UI空间
        任务栏高度 = 60
        系统边距 = 40
        可用宽度 = 屏幕宽度 - 系统边距
        可用高度 = 屏幕高度 - 任务栏高度 - 系统边距

        # 根据屏幕分辨率智能调整
        if 屏幕宽度 >= 2560:  # 2K/4K屏幕
            窗口宽度 = min(1400, int(可用宽度 * 0.75))
            窗口高度 = min(900, int(可用高度 * 0.8))
        elif 屏幕宽度 >= 1920:  # 1080P屏幕
            窗口宽度 = min(1200, int(可用宽度 * 0.8))
            窗口高度 = min(750, int(可用高度 * 0.85))
        elif 屏幕宽度 >= 1600:  # 中等屏幕
            窗口宽度 = min(1000, int(可用宽度 * 0.85))
            窗口高度 = min(650, int(可用高度 * 0.9))
        elif 屏幕宽度 >= 1366:  # 普通屏幕
            窗口宽度 = min(900, int(可用宽度 * 0.9))
            窗口高度 = min(600, int(可用高度 * 0.9))
        else:  # 小屏幕
            窗口宽度 = min(800, int(可用宽度 * 0.95))
            窗口高度 = min(550, int(可用高度 * 0.95))

        # 确保最小尺寸
        窗口宽度 = max(750, 窗口宽度)
        窗口高度 = max(500, 窗口高度)

        return 窗口宽度, 窗口高度

    def 计算预览画布尺寸(窗口宽度, 窗口高度):
        """根据窗口尺寸计算最佳预览画布尺寸"""
        左侧宽度 = 300  # 增加左侧宽度以容纳更好的滑块
        边距总和 = 60  # 左右边距 + 中间间距

        可用宽度 = 窗口宽度 - 左侧宽度 - 边距总和
        可用高度 = 窗口高度 - 200  # 减去标题、按钮等UI元素高度

        # 计算画布尺寸，保持合理比例
        画布宽度 = max(400, min(800, 可用宽度))
        画布高度 = max(300, min(600, 可用高度))

        # 调整比例，确保预览效果最佳
        if 画布宽度 / 画布高度 > 1.6:
            画布高度 = int(画布宽度 / 1.4)
        elif 画布高度 / 画布宽度 > 1.2:
            画布宽度 = int(画布高度 * 1.2)

        return 画布宽度, 画布高度

    # 创建主窗口
    root = tk.Tk()
    root.title("设备配置")
    root.resizable(True, True)  # 允许调整大小
    root.configure(bg='#f5f5f5')
    root.minsize(750, 500)  # 设置更合理的最小尺寸

    # 计算最佳窗口尺寸
    窗口宽度, 窗口高度 = 计算最佳窗口尺寸()
    画布宽度, 画布高度 = 计算预览画布尺寸(窗口宽度, 窗口高度)

    # 主框架
    main_frame = tk.Frame(root, bg='#f5f5f5', padx=15, pady=15)
    main_frame.pack(fill="both", expand=True)

    # 左侧配置区域 - 固定宽度
    left_frame = tk.Frame(main_frame, bg='#f5f5f5', width=300)
    left_frame.pack(side="left", fill="y", padx=(0, 20))
    left_frame.pack_propagate(False)

    # 右侧预览区域
    right_frame = tk.Frame(main_frame, bg='#f5f5f5')
    right_frame.pack(side="right", fill="both", expand=True)

    # 1. 型号选择 - 清晰直观的设计
    model_frame = tk.LabelFrame(left_frame, text="📱 设备型号 (必选)", padx=15, pady=12,
                               font=('Arial', 11, 'bold'), bg='#f5f5f5', fg='#d32f2f')
    model_frame.pack(fill="x", pady=(0, 15))

    model_var = tk.StringVar()
    model_var.set("NONE_SELECTED")  # 设置一个不存在的值，确保没有选中

    # 添加提示标签
    tip_label = tk.Label(model_frame, text="请选择设备型号：",
                        font=('Arial', 9), fg='#666666', bg='#f5f5f5')
    tip_label.pack(anchor=tk.W, padx=5, pady=(0, 8))

    # 创建型号选择按钮，使用更清晰的样式
    for model_id, model_info in config['models'].items():
        radio_btn = tk.Radiobutton(model_frame,
                                  text=f"  {model_info['display_name']}",
                                  variable=model_var,
                                  value=model_id,
                                  font=('Arial', 10, 'bold'),
                                  bg='#f5f5f5',
                                  activebackground='#e3f2fd',
                                  selectcolor='#4caf50',
                                  indicatoron=True,
                                  padx=10, pady=5)
        radio_btn.pack(anchor=tk.W, padx=20, pady=4, fill='x')

    # 2. 设备配置（直接显示滑块）
    config_frame = tk.LabelFrame(left_frame, text="🛠️ 设备配置", padx=10, pady=8,
                                font=('Arial', 10, 'bold'), bg='#f5f5f5')
    config_frame.pack(fill="x", pady=(0, 15))

    # 设备总数滑块
    tk.Label(config_frame, text="设备总数：", font=('Arial', 10), bg='#f5f5f5').grid(
        row=0, column=0, sticky="w", padx=8, pady=6)
    device_scale = tk.Scale(config_frame, from_=4, to=64, orient=tk.HORIZONTAL,
                           length=200, bg='#f5f5f5', font=('Arial', 9))
    device_scale.set(12)
    device_scale.grid(row=0, column=1, padx=8, pady=6, sticky="ew")

    # 每行数量滑块
    tk.Label(config_frame, text="每行数量：", font=('Arial', 10), bg='#f5f5f5').grid(
        row=1, column=0, sticky="w", padx=8, pady=6)
    rows_scale = tk.Scale(config_frame, from_=2, to=15, orient=tk.HORIZONTAL,
                         length=200, bg='#f5f5f5', font=('Arial', 9))
    rows_scale.set(4)
    rows_scale.grid(row=1, column=1, padx=8, pady=6, sticky="ew")

    # 配置grid权重
    config_frame.grid_columnconfigure(1, weight=1)



    # 3. 预览区域
    preview_frame = tk.LabelFrame(right_frame, text="👁️ 布局预览", padx=10, pady=8,
                                 font=('Arial', 11, 'bold'), bg='#f5f5f5')
    preview_frame.pack(fill="both", expand=True, pady=(0, 15))

    # 预览画布容器
    canvas_container = tk.Frame(preview_frame, bg='#f5f5f5')
    canvas_container.pack(fill="both", expand=True, pady=5)

    preview_canvas = tk.Canvas(canvas_container, width=画布宽度, height=画布高度,
                              bg="white", relief="sunken", borderwidth=1)
    preview_canvas.pack(expand=True)

    info_label = tk.Label(preview_frame, text="", font=('Arial', 10, 'bold'),
                         fg="#1976D2", bg='#f5f5f5')
    info_label.pack(pady=5)

    # 4. 确认按钮
    button_frame = tk.Frame(right_frame, bg='#f5f5f5')
    button_frame.pack(fill="x")

    confirm_button = tk.Button(button_frame, text="✅ 确认配置",
                              padx=30, pady=10, font=('Arial', 11, 'bold'),
                              bg="#4CAF50", fg="white", relief="raised")
    confirm_button.pack()

    # === 事件处理函数 ===

    def 更新预览():
        if not current_layout:
            return

        preview_canvas.delete("all")
        总数量 = current_layout["总数量"]
        每行数量 = current_layout["每行数量"]
        行数 = current_layout["行数"]

        # 获取当前画布尺寸
        canvas_width = preview_canvas.winfo_width()
        canvas_height = preview_canvas.winfo_height()

        if canvas_width <= 1 or canvas_height <= 1:  # 画布未初始化
            canvas_width, canvas_height = 画布宽度, 画布高度

        # 使用统一的窗格尺寸计算函数
        实际窗格宽度, 实际窗格高度, 实际窗格间距 = 计算实际窗格尺寸(每行数量, 行数)

        # 按比例缩放到预览画布
        预览边距 = 20
        预览可用宽度 = canvas_width - 预览边距 * 2
        预览可用高度 = canvas_height - 预览边距 * 2

        # 计算缩放比例
        实际总宽度 = 每行数量 * 实际窗格宽度 + (每行数量 - 1) * 实际窗格间距
        实际总高度 = 行数 * 实际窗格高度 + (行数 - 1) * 实际窗格间距

        宽度缩放比例 = 预览可用宽度 / 实际总宽度 if 实际总宽度 > 0 else 1
        高度缩放比例 = 预览可用高度 / 实际总高度 if 实际总高度 > 0 else 1
        缩放比例 = min(宽度缩放比例, 高度缩放比例, 1.0)  # 不放大，只缩小

        # 计算预览窗格尺寸
        窗格宽度 = max(15, int(实际窗格宽度 * 缩放比例))
        窗格高度 = max(12, int(实际窗格高度 * 缩放比例))
        预览窗格间距 = max(2, int(实际窗格间距 * 缩放比例))

        # 计算起始位置（居中）
        总宽度 = 每行数量 * 窗格宽度 + (每行数量 - 1) * 预览窗格间距
        总高度 = 行数 * 窗格高度 + (行数 - 1) * 预览窗格间距
        start_x = (canvas_width - 总宽度) // 2
        start_y = (canvas_height - 总高度) // 2

        # 绘制完整的网格（包括空窗格）
        总网格数 = 行数 * 每行数量
        for i in range(总网格数):
            行 = i // 每行数量
            列 = i % 每行数量
            x1 = start_x + 列 * (窗格宽度 + 预览窗格间距)
            y1 = start_y + 行 * (窗格高度 + 预览窗格间距)
            x2 = x1 + 窗格宽度
            y2 = y1 + 窗格高度

            # 判断是否为有效设备窗格
            if i < 总数量:
                # 有效设备窗格 - 与实际窗格相同的样式
                阴影偏移 = max(1, 窗格宽度 // 30)
                preview_canvas.create_rectangle(x1 + 阴影偏移, y1 + 阴影偏移, x2 + 阴影偏移, y2 + 阴影偏移,
                                              fill="#c0c0c0", outline="", width=0)  # 阴影

                # 主窗格 - 使用与实际窗格相同的颜色
                边框宽度 = max(1, min(2, 窗格宽度 // 25))
                preview_canvas.create_rectangle(x1, y1, x2, y2,
                                              fill="#f8f9fa", outline="#666666", width=边框宽度)

                # 添加设备编号
                if 窗格宽度 >= 25:
                    字体大小 = max(8, min(12, 窗格宽度 // 4))
                    preview_canvas.create_text((x1 + x2) // 2, (y1 + y2) // 2,
                                             text=str(i + 1), font=('Arial', 字体大小, 'bold'), fill="#1976d2")
                elif 窗格宽度 >= 18:
                    字体大小 = max(6, 窗格宽度 // 3)
                    preview_canvas.create_text((x1 + x2) // 2, (y1 + y2) // 2,
                                             text=str(i + 1), font=('Arial', int(字体大小)), fill="#1976d2")
            else:
                # 空窗格 - 虚线边框
                虚线样式 = (max(2, 窗格宽度 // 12), max(2, 窗格宽度 // 12))
                preview_canvas.create_rectangle(x1, y1, x2, y2,
                                              fill="#fafafa", outline="#cccccc", width=1, dash=虚线样式)

    def 更新信息显示():
        if not current_layout:
            return
        总数量 = current_layout["总数量"]
        每行数量 = current_layout["每行数量"]
        行数 = current_layout["行数"]

        # 使用统一的窗格尺寸计算函数
        实际窗格宽度, 实际窗格高度, _ = 计算实际窗格尺寸(每行数量, 行数)

        info_label.config(text=f"布局：{行数}行 × {每行数量}列 = {总数量}个窗格 | 窗格尺寸：{实际窗格宽度}×{实际窗格高度}px")

    def on_scale_change(val=None):
        nonlocal current_layout
        总数量 = int(device_scale.get())
        每行数量 = int(rows_scale.get())
        行数 = 总数量 // 每行数量 + (1 if 总数量 % 每行数量 else 0)
        current_layout = {"总数量": 总数量, "每行数量": 每行数量, "行数": 行数}
        更新预览()
        更新信息显示()



    def select_device():
        nonlocal selected_device, selected_rows, selected_model
        selected_model = model_var.get()
        if not selected_model or selected_model == "NONE_SELECTED":
            messagebox.showwarning("警告", "请选择设备型号!")
            return

        selected_device = int(device_scale.get())
        selected_rows = int(rows_scale.get())

        model_folders = [f for f in os.listdir() if os.path.isdir(f) and selected_model.lower() in f.lower()]
        if not model_folders:
            messagebox.showerror("错误", f"当前目录下未找到包含 {selected_model} 的文件夹!")
            return

        os.chdir(model_folders[0])
        root.destroy()

    def on_window_resize(event=None):
        """窗口大小改变时的处理"""
        if event and event.widget == root:
            root.after_idle(更新预览)  # 延迟更新预览

    # === 绑定事件 ===
    device_scale.config(command=on_scale_change)
    rows_scale.config(command=on_scale_change)
    confirm_button.config(command=select_device)
    root.bind('<Configure>', on_window_resize)

    # 设置默认值 - 不选中任何型号
    on_scale_change()  # 初始化预览

    # 设置窗口位置和大小
    x = (屏幕宽度 - 窗口宽度) // 2
    y = (屏幕高度 - 窗口高度) // 2
    root.geometry(f'{窗口宽度}x{窗口高度}+{x}+{y}')

    root.mainloop()

    # 检查返回值
    if selected_device > 0 and selected_rows > 0 and selected_model:
        return selected_device, selected_rows, selected_model
    else:
        logger.info("用户取消了配置选择")
        sys.exit()


if __name__ == '__main__':
    # 设置fastboot路径
    fastboot = 'fastboot'
    chkcode = os.path.join(os.getcwd(), 'chkcode.bin')
    # 加载配置文件
    加载配置文件()
    try:
        初始格子数量, 每行格子数, 选择型号 = 初始窗口()
    except Exception as e:
        logger.error(f"配置界面错误: {str(e)}")
        sys.exit()
    logger.info(f"选择型号: {选择型号}")
    # 设置全局变量
    完成 = {}
    fastboot_sizes = {
        "高速率-32M": '32M',
        "满速率-64M": '64M',
        "极限速率-128M": '128M',
        "超级速率-256M": '256M',
        "极速模式-512M": '512M',
        "最高速率-1G": '1G',
        '不限速': '0'
    }
    解bl = set()

    # 计算行数
    行数 = 初始格子数量 // 每行格子数
    if 初始格子数量 % 每行格子数 != 0:
        行数 += 1

    # 全屏自适应窗格算法
    屏幕宽度, 屏幕高度 = 检测屏幕信息()

    # 主窗口全屏（减去任务栏）
    主窗口宽度 = 屏幕宽度
    主窗口高度 = 屏幕高度 - 80  # 减去任务栏高度

    # 使用统一的窗格尺寸计算函数
    格子窗口宽度, 格子窗口高度, _ = 计算实际窗格尺寸(每行格子数, 行数)

    # 智能缩放算法 - 基于窗格尺寸和设备密度
    窗格面积 = 格子窗口宽度 * 格子窗口高度
    窗格高度比 = 格子窗口高度 / 300  # 以300px为基准高度
    设备密度 = 初始格子数量 / 64  # 以64个设备为基准密度

    # 智能字体大小：综合考虑窗格面积和密度
    窗口字体大小 = max(8, min(16, int(12 * 窗格高度比 / (设备密度 ** 0.3))))

    窗口字体大小 = max(8, min(18, 窗口字体大小))

    # 这些变量已经是全局变量了，不需要额外设置

    # 输出系统信息
    屏幕利用率 = (主窗口宽度 * 主窗口高度) / (屏幕宽度 * 屏幕高度) * 100
    logger.info(f"系统初始化完成 - 屏幕:{屏幕宽度}×{屏幕高度} 窗口:{主窗口宽度}×{主窗口高度} 利用率:{屏幕利用率:.1f}%")
    logger.info(f"设备布局: {行数}行×{每行格子数}列={初始格子数量}个 窗格:{格子窗口宽度}×{格子窗口高度}px")

    # 启动主程序
    Fastboot刷写工具()
